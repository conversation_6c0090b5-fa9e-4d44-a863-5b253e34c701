using CrystalInfoFramework, FilePaths, Crystalline

# Note: RawCifData will be available from the main.jl context when this file is included

# function to read cif file and return a Structure
function read_cif_file(filename)::RawCifData
  cif_file = Cif(filename)
  my_block = first(cif_file).second

  possible_symmetry_keys = [
    "_space_group_symop_operation_xyz",
    "_symmetry_equiv.pos_as_xyz",
    "_symmetry_equiv_pos_as_xyz",
  ]

  local symmetries_from_cif_file::Vector{String}

  for key in possible_symmetry_keys
    if haskey(my_block, key)
      symmetries_from_cif_file = my_block[key]
      break
    end
  end

  if !@isdefined(symmetries_from_cif_file)
    error("No symmetry operations found in file $filename")
  end

  if haskey(my_block, "_space_group_IT_number")
    space_group_number = parse(UInt, first(my_block["_space_group_IT_number"]))
  else
    space_group_number = UInt(0)
  end


  # transpose _atom_site_fract_* to make array of points 
  base_points = hcat(my_block["_atom_site_fract_x"], my_block["_atom_site_fract_y"], my_block["_atom_site_fract_z"])

  # Extract Z parameter for unit cell atom count calculation
  z_parameter = if haskey(my_block, "_cell_formula_units_Z")
    parse(Int, first(my_block["_cell_formula_units_Z"]))
  else
    1  # Default to 1 if not specified
  end

  return RawCifData(
    space_group_number,
    transform_symmetries(symmetries_from_cif_file),
    my_block["_atom_site_label"],
    base_points,
    z_parameter)
end

function transform_symmetries(symmetries::Vector{String})::Vector{SymOperation{3}}
  symmetryOperations = Vector{SymOperation{3}}(undef, length(symmetries))

  for (i, symmetry) in enumerate(symmetries)
    symmetryOperations[i] = SymOperation{3}(parse_symmetry(symmetry))
  end

  return symmetryOperations
end

function parse_symmetry(symmetry::String)::String
  parts = split(symmetry, ',')
  new_parts = [parse_symmetry_part(String(strip(part))) for part in parts]
  return join(new_parts, ',')
end

function parse_symmetry_part(part::String)::String
  if !contains(part, '/') || match(r"^-?\w([+-]\d+/\d+)$", part) !== nothing
    # Simple case: no fraction or already in correct format
    return part
  end

  fraction_first_pattern = r"^([-]?\d\/\d)\+?([+\-\w]+)$"

  matched_patterns = match(fraction_first_pattern, part)
  if isnothing(matched_patterns)
    error("Symmetry $part does not match pattern $fraction_first_pattern")
  end

  fraction = matched_patterns.captures[1]
  variables = matched_patterns.captures[2]

  if match(r"^\d", fraction) !== nothing
    fraction = "+" * fraction
  end

  return "$variables$fraction"
end
