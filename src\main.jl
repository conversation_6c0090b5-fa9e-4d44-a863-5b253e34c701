include("structs/structs.jl")
using .Structs

include("types/types.jl")
using .Types

include("utils.jl")
include("func.jl")
include("cif/readCifFile.jl")
include("cif/getSymmetryPoints.jl")

filename = p"C:\\Users\\<USER>\\Documents\\Repos\\julia\\crystal-symmetry-seeker\\examples\\2101167.cif"

function test_supercell_validation(superCellSize::Int)
  println("\n=== Testing supercell size: $(superCellSize)x$(superCellSize)x$(superCellSize) ===")

  raw_cif_data = read_cif_file(filename)
  super_cell = get_super_cell(raw_cif_data, superCellSize)

  println("Supercell points count: ", length(super_cell.points))

  # Calculate expected count dynamically from unit cell
  unit_cell_count = calculate_unit_cell_atom_count(raw_cif_data)
  expected_count = unit_cell_count * superCellSize^3
  println("Unit cell atom count: ", unit_cell_count)
  println("Expected supercell count: ", expected_count)

  # Primary validation: check if the count matches expected
  count_matches = length(super_cell.points) == expected_count
  println("Count matches expected: ", count_matches)

  # CORRECT symmetry closure validation:
  # The supercell should be closed under symmetry operations
  # This means: applying symmetries should not create new points

  # Use the proper validation method that handles coordinate systems correctly
  closure_validation = validate_supercell_symmetry_closure(raw_cif_data, super_cell.points, superCellSize)

  println("Symmetry closure validation: ", closure_validation)
  println("are they the same?: ", closure_validation && count_matches)

  return closure_validation && count_matches
end

# Test multiple supercell sizes
println("Testing supercell validation for sizes 1-10:")
all_passed = true
for size in 1:10
  passed = test_supercell_validation(size)
  if !passed
    all_passed = false
    println("❌ FAILED for size $size")
  else
    println("✅ PASSED for size $size")
  end
end

println("\n=== SUMMARY ===")
if all_passed
  println("✅ All supercell sizes (1-10) passed validation!")
else
  println("❌ Some supercell sizes failed validation")
end
