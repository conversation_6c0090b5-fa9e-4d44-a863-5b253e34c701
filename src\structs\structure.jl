
using Crystalline: SymOperation
include("symmetryComplex.jl")

struct RawCifData
  sg_num::UInt
  symmetry::Vector{SymOperation{3}}
  labels::Vector{String}
  base_points::Matrix{String}
  z_parameter::Int  # Number of formula units in unit cell
end

struct MySuperCell
  sg_num::UInt
  points::Vector{Vector{Rational{Int}}}
  symmetries::Vector{SymmetryComplex}
  labels::Vector{String}
  size::Int
end

