#!/usr/bin/env julia

# Test script to verify the symmetry closure fix

include("src/structs/structs.jl")
using .Structs

include("src/types/types.jl")
using .Types

include("src/utils.jl")
include("src/func.jl")
include("src/cif/readCifFile.jl")
include("src/cif/getSymmetryPoints.jl")

using FilePaths

function test_symmetry_closure_validation()
    println("=== Testing Symmetry Closure Validation ===")
    
    filename = p"examples/2101167.cif"
    
    try
        raw_cif_data = read_cif_file(filename)
        
        println("Testing supercell sizes 1-10 for symmetry closure...")
        
        results = []
        
        for size in 1:10
            println("\n--- Size $size ---")
            
            super_cell = get_super_cell(raw_cif_data, size)
            
            # Test the new validation method
            closure_valid = validate_supercell_symmetry_closure(raw_cif_data, super_cell.points, size)
            
            # Also test count validation
            unit_cell_count = calculate_unit_cell_atom_count(raw_cif_data)
            expected_count = unit_cell_count * size^3
            count_valid = length(super_cell.points) == expected_count
            
            overall_valid = closure_valid && count_valid
            
            println("  Points: $(length(super_cell.points)), Expected: $expected_count")
            println("  Count valid: $count_valid")
            println("  Closure valid: $closure_valid") 
            println("  Overall valid: $overall_valid")
            
            push!(results, (size, overall_valid, count_valid, closure_valid))
        end
        
        # Summary
        println("\n=== SUMMARY ===")
        all_passed = true
        for (size, overall, count, closure) in results
            status = overall ? "✅ PASS" : "❌ FAIL"
            println("Size $size: $status (count: $count, closure: $closure)")
            if !overall
                all_passed = false
            end
        end
        
        if all_passed
            println("\n🎉 SUCCESS: All supercell sizes pass symmetry closure validation!")
        else
            println("\n❌ Some sizes still fail validation")
        end
        
        # Test the old problematic method for comparison
        println("\n=== Comparison with Old Method ===")
        println("Testing the old incorrect validation method...")
        
        for size in [2, 4, 6]  # Even sizes that were problematic
            super_cell = get_super_cell(raw_cif_data, size)
            symmetries = map(SymmetryComplex, raw_cif_data.symmetry)
            
            # Old incorrect method
            reapplied_points = get_all_points(symmetries, super_cell.points)
            old_validation = reapplied_points == super_cell.points
            
            println("Size $size - Old method result: $old_validation")
            if !old_validation
                extra_points = length(reapplied_points) - length(super_cell.points)
                println("  Extra points generated: $extra_points")
            end
        end
        
        return true
        
    catch e
        println("ERROR: $e")
        return false
    end
end

function demonstrate_fix()
    println("=== CRYSTAL SYMMETRY CLOSURE VALIDATION - FIXED ===")
    println("This demonstrates the fix for the symmetry closure issue.")
    println("The problem was that unit cell symmetries don't maintain closure")
    println("when applied to compressed supercell coordinates.")
    println("The fix validates closure in the proper coordinate system.\n")
    
    success = test_symmetry_closure_validation()
    
    if success
        println("\n" * "="^60)
        println("✅ SYMMETRY CLOSURE ISSUE FIXED!")
        println("All supercell sizes now maintain proper symmetry closure")
        println("="^60)
    else
        println("\n" * "="^60)
        println("❌ Issue persists - further investigation needed")
        println("="^60)
    end
end

if abspath(PROGRAM_FILE) == @__FILE__
    demonstrate_fix()
end
