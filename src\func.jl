using Crystalline
using LinearAlgebra

include("utils.jl")

include("types/types.jl")
import .Types: PointValue, Point, Cell, Orbit

import .Structs: RawCifData, MySuperCell, SymmetryComplex

function prepareMatrixTransformation(op::SymOperation{3})
  rotation = op.rotation
  rational_rotation = transformFloatToRational.(rotation, 6)
  translation = op.translation
  rational_translation = transformTranslationToRational(translation)
  full_matrix = [rational_rotation rational_translation; 0 0 0 1]
  return full_matrix
end

# function to multiply a symmetry operation with a point and normalize to 3D
function mul(op::Matrix{PointValue}, point::Point)::Point
  new_point = op * point
  truncated_point = put_point_into_range_4D(new_point)
  return truncated_point
end


# function to put a 3D point into range from 0 to 1 (for first 3 coordinates)
function put_point_into_range_4D(point::Point)::Point
  modded_point = mod.(point, 1)
  modded_point[4] = 1
  return modded_point
end

# function to put a 3D point into range from -1 to 1
# function put_point_into_range_4D(point::Point)::Point
#   x, y, z, w = point


#   if x <= -1
#     x += 2
#   elseif x >= 1
#     x -= 2
#   end
#   if y <= -1
#     y += 2
#   elseif y >= 1
#     y -= 2
#   end
#   if z <= -1
#     z += 2
#   elseif z >= 1
#     z -= 2
#   end


#   return [x, y, z, w]
# end

function make_supercell(points::Cell, supercell_size::Int)::Cell
  total_points = length(points) * supercell_size^3
  supercell_points = Vector{Vector{Rational{Int}}}(undef, total_points)
  translations::Array{Tuple{Int,Int,Int}} = [(i, j, k) for i in 0:supercell_size-1,
                                             j in 0:supercell_size-1,
                                             k in 0:supercell_size-1]

  # Fill supercell points array
  for (idx, (point, translation)) in enumerate(Iterators.product(points, translations))
    i, j, k = translation
    x, y, z = point[1:3]
    supercell_points[idx] = [x + i, y + j, z + k, 1 // 1]
  end

  return supercell_points
end

# function to calculate the inverses of symmetry operations, excluding redundant ones.
# Returns: Array of inverse symmetry operations that don't overlap with existing operations
function get_inverse_symmetry_operations(symmetries::Vector{SymOperation{3}})::Vector{Crystalline.AbstractOperation{3}}
  inverse_symmetries::Vector{Crystalline.AbstractOperation{3}} = []
  for symmetry in symmetries
    inverse_op = inv(symmetry)
    if !(inverse_op in symmetries)
      push!(inverse_symmetries, inverse_op)
    end
  end
  return inverse_symmetries
end

# function to compress supercell. Proportionally squash points within the unit cell. The last column stays the same. Result is sorted.
function compress_supercell(points::Cell, supercell_size::Int)::Cell
  compressed_points = Vector{Vector{Rational{Int}}}(undef, length(points))
  for (i, point) in enumerate(points)
    xyzw = point .// supercell_size
    compressed_points[i] = [xyzw[1], xyzw[2], xyzw[3], 1 // 1]
  end
  sort(compressed_points)
end

# Function to calculate the number of atoms in the unit cell dynamically
function calculate_unit_cell_atom_count(raw_cif_data::RawCifData)::Int
  # Method 1: Use symmetry operations to generate all unique positions
  symmetry_points = parse_points(raw_cif_data)
  symmetries::Vector{SymmetryComplex} = map(SymmetryComplex, raw_cif_data.symmetry)
  unit_cell = get_all_points(symmetries, symmetry_points)

  return length(unit_cell)
end

# Alternative method: Calculate from Z parameter and formula composition
function calculate_unit_cell_atom_count_from_z(raw_cif_data::RawCifData)::Int
  # This would require parsing the chemical formula, which is more complex
  # For now, we use the symmetry-based method above
  # Future enhancement: parse _chemical_formula_sum to get atoms per formula unit
  # and multiply by Z parameter
  return calculate_unit_cell_atom_count(raw_cif_data)
end

function get_super_cell(raw_cif_data::RawCifData, superCellSize::Int)::MySuperCell

  symmetry_points = parse_points(raw_cif_data)
  symmetries::Vector{SymmetryComplex} = map(SymmetryComplex, raw_cif_data.symmetry)

  unit_cell = get_all_points(symmetries, symmetry_points)
  supercell_points = make_supercell(unit_cell, superCellSize)
  compressed_cell = compress_supercell(supercell_points, superCellSize)

  return MySuperCell(
    raw_cif_data.sg_num,
    compressed_cell,
    symmetries,
    raw_cif_data.labels,
    superCellSize,
  )
end

function isTheSamePoint(point1::Point, point2::Point)::Bool
  all(point1 .== point2)
end

function isPointInOrbit(point::Point, orbit::Orbit)::Bool
  any(p -> isTheSamePoint(p, point), orbit)
end

function isPointInAnyOrbit(point::Point, orbits::Vector{Orbit})::Bool
  any(orbit -> isPointInOrbit(point, orbit), orbits)
end


# TODO: function get_orbit_for_point
# TODO: function get_orbits_for_symmetry

# function to make all possible orbits of points. Checks which points are in the same orbit and groups them together. 
function get_all_orbits(symmetries::Vector{SymmetryComplex}, points::Cell)::Dict{SymmetryComplex,Vector{Orbit}}
  # for each symmetry there is a array of arrays of points. 
  orbits = Dict{SymmetryComplex,Vector{Orbit}}(symmetry => [] for symmetry in symmetries)


  j = 0
  points_seen_amount = 0

  for (symmetry, orbit) in orbits
    points_seen = Set{UInt}()

    for point in points
      site_symmetry::Vector{Point} = [point]
      push!(points_seen, hash(point))

      transformed_point::Point = mul(symmetry.op_rational, point)

      if !isPointInOrbit(transformed_point, points)
        println("Point not found")
        println("point: ", point)
        println("transformed_point: ", transformed_point)
        println("symmetry: ", symmetry.seitz, " ", symmetry.op_rational)
        transformed_point = findApproximatePoint(transformed_point, points)
        throw("Point not found")
      end

      if pointWasSeen(transformed_point, points_seen)
        points_seen_amount += 1

        if length(site_symmetry) == 1 && isTheSamePoint(point, transformed_point)
          push!(orbit, site_symmetry)
        end
        continue
      end

      i = 0
      while !isPointInOrbit(transformed_point, site_symmetry) # check if exist an orbit with the point. 
        push!(site_symmetry, transformed_point)
        push!(points_seen, hash(transformed_point))

        transformed_point = mul(symmetry.op_rational, transformed_point)

        i += 1
        if i > length(points)
          println("i > length(points)")
          push!(orbit, site_symmetry)

          return orbits
        end
      end

      push!(orbit, site_symmetry)
    end
    j += 1
  end

  println("points_seen_amount: ", points_seen_amount)
  return orbits
end
# get_all_orbits(raw_cif_data.symmetry, super_cell.points)
# myDict = get_all_orbits(super_cell.symmetry, super_cell.points)

# @code_warntype get_all_orbits(super_cell.symmetry, super_cell.points)

# # print sum of length of all orbits for each symmetry
# for (symmetry, orbit) in myDict
#   println(symmetry, " ", length(orbit))
# end
