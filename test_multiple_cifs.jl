#!/usr/bin/env julia

# Test the fix with multiple CIF files to ensure generality

include("src/structs/structs.jl")
using .Structs

include("src/types/types.jl")
using .Types

include("src/utils.jl")
include("src/func.jl")
include("src/cif/readCifFile.jl")
include("src/cif/getSymmetryPoints.jl")

using FilePaths

function test_cif_file(filename::AbstractPath, max_size::Int = 5)
    println("\n" * "="^60)
    println("Testing CIF file: $filename")
    println("="^60)
    
    try
        raw_cif_data = read_cif_file(filename)
        
        println("Space group number: ", raw_cif_data.sg_num)
        println("Number of symmetry operations: ", length(raw_cif_data.symmetry))
        println("Z parameter: ", raw_cif_data.z_parameter)
        println("Number of atom sites: ", length(raw_cif_data.labels))
        
        # Calculate unit cell atom count
        unit_cell_count = calculate_unit_cell_atom_count(raw_cif_data)
        println("Unit cell atom count: ", unit_cell_count)
        
        println("\nTesting supercell sizes 1-$max_size:")
        
        all_passed = true
        
        for size in 1:max_size
            super_cell = get_super_cell(raw_cif_data, size)
            
            # Count validation
            expected_count = unit_cell_count * size^3
            count_valid = length(super_cell.points) == expected_count
            
            # Symmetry closure validation
            closure_valid = validate_supercell_symmetry_closure(raw_cif_data, super_cell.points, size)
            
            overall_valid = count_valid && closure_valid
            
            status = overall_valid ? "✅ PASS" : "❌ FAIL"
            println("  Size $size: $status ($(length(super_cell.points))/$expected_count points, closure: $closure_valid)")
            
            if !overall_valid
                all_passed = false
            end
        end
        
        if all_passed
            println("✅ All sizes passed for this CIF file!")
        else
            println("❌ Some sizes failed for this CIF file")
        end
        
        return all_passed
        
    catch e
        println("❌ ERROR processing $filename: $e")
        return false
    end
end

function test_multiple_cifs()
    println("=== TESTING MULTIPLE CIF FILES ===")
    println("Verifying that the fix works for different crystal structures")
    
    cif_files = [
        p"examples/2101167.cif",  # Hematite (original test case)
        p"examples/1000003.cif",  # Different structure
        p"examples/1000032.cif",  # Different structure
        p"examples/1001686.cif",  # Different structure
        p"examples/ZnS-Sfaleryt.cif"  # ZnS structure
    ]
    
    results = []
    
    for cif_file in cif_files
        if isfile(cif_file)
            success = test_cif_file(cif_file, 3)  # Test smaller sizes for speed
            push!(results, (cif_file, success))
        else
            println("⚠️  File not found: $cif_file")
            push!(results, (cif_file, false))
        end
    end
    
    println("\n" * "="^60)
    println("SUMMARY OF ALL CIF FILES")
    println("="^60)
    
    all_passed = true
    for (filename, success) in results
        status = success ? "✅ PASS" : "❌ FAIL"
        println("$filename: $status")
        if !success
            all_passed = false
        end
    end
    
    println("\n" * "="^60)
    if all_passed
        println("🎉 SUCCESS: Fix works for ALL tested CIF files!")
        println("The solution is GENERAL and not specific to hematite.")
        println("✅ Dynamic atom count calculation works correctly")
        println("✅ Symmetry closure validation works for different structures")
    else
        println("❌ Some CIF files failed validation")
        println("The fix may need further refinement for edge cases")
    end
    println("="^60)
    
    return all_passed
end

if abspath(PROGRAM_FILE) == @__FILE__
    test_multiple_cifs()
end
