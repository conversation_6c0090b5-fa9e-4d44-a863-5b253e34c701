#!/usr/bin/env julia

# Final verification script for the symmetry closure fix

include("src/structs/structs.jl")
using .Structs

include("src/types/types.jl")
using .Types

include("src/utils.jl")
include("src/func.jl")
include("src/cif/readCifFile.jl")
include("src/cif/getSymmetryPoints.jl")

using FilePaths

function verify_final_fix()
    println("=== FINAL VERIFICATION: Symmetry Closure Fix ===")
    
    filename = p"examples/2101167.cif"
    
    try
        raw_cif_data = read_cif_file(filename)
        
        println("CIF file loaded successfully")
        println("Space group number: ", raw_cif_data.sg_num)
        println("Number of symmetry operations: ", length(raw_cif_data.symmetry))
        println("Z parameter: ", raw_cif_data.z_parameter)
        
        # Calculate unit cell atom count
        unit_cell_count = calculate_unit_cell_atom_count(raw_cif_data)
        println("Unit cell atom count: ", unit_cell_count)
        
        println("\nTesting supercell validation for sizes 1-10:")
        println("Size | Points | Expected | Count✓ | Closure✓ | Overall✓")
        println("-----|--------|----------|--------|----------|----------")
        
        all_passed = true
        
        for size in 1:10
            super_cell = get_super_cell(raw_cif_data, size)
            
            # Count validation
            expected_count = unit_cell_count * size^3
            count_valid = length(super_cell.points) == expected_count
            
            # Symmetry closure validation
            closure_valid = validate_supercell_symmetry_closure(raw_cif_data, super_cell.points, size)
            
            overall_valid = count_valid && closure_valid
            
            count_symbol = count_valid ? "✅" : "❌"
            closure_symbol = closure_valid ? "✅" : "❌"
            overall_symbol = overall_valid ? "✅" : "❌"
            
            println("$size    | $(length(super_cell.points))    | $expected_count     | $count_symbol      | $closure_symbol        | $overall_symbol")
            
            if !overall_valid
                all_passed = false
            end
        end
        
        println("\n=== RESULTS ===")
        if all_passed
            println("🎉 SUCCESS: All supercell sizes (1-10) pass validation!")
            println("✅ Count validation: All sizes have correct atom counts")
            println("✅ Closure validation: All sizes maintain symmetry closure")
            println("✅ The symmetry closure issue has been COMPLETELY FIXED!")
        else
            println("❌ Some sizes still fail validation")
            println("Further investigation needed")
        end
        
        # Test with the original main.jl approach
        println("\n=== Testing main.jl validation ===")
        
        # Simulate what main.jl does now
        test_size = 2  # Previously problematic even size
        super_cell = get_super_cell(raw_cif_data, test_size)
        
        unit_cell_count = calculate_unit_cell_atom_count(raw_cif_data)
        expected_count = unit_cell_count * test_size^3
        count_matches = length(super_cell.points) == expected_count
        
        closure_validation = validate_supercell_symmetry_closure(raw_cif_data, super_cell.points, test_size)
        
        main_validation = closure_validation && count_matches
        
        println("Size $test_size validation (main.jl approach): $main_validation")
        println("  Count matches: $count_matches")
        println("  Closure valid: $closure_validation")
        println("  are they the same?: $main_validation")
        
        if main_validation
            println("✅ main.jl now correctly validates supercells!")
        else
            println("❌ main.jl validation still fails")
        end
        
        return all_passed
        
    catch e
        println("ERROR: $e")
        println("Stack trace:")
        for (exc, bt) in Base.catch_stack()
            showerror(stdout, exc, bt)
            println()
        end
        return false
    end
end

if abspath(PROGRAM_FILE) == @__FILE__
    success = verify_final_fix()
    
    println("\n" * "="^60)
    if success
        println("✅ VERIFICATION COMPLETE: Fix is working correctly!")
        println("The symmetry closure issue has been resolved.")
        println("All supercell sizes now pass validation.")
    else
        println("❌ VERIFICATION FAILED: Issues remain")
        println("Further debugging required.")
    end
    println("="^60)
end
